const { Pool } = require('pg');

const pool = new Pool({
  host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
  database: 'neondb',
  user: 'neondb_owner',
  password: 'npg_v1aKnJdNXif4',
  port: 5432,
  ssl: { rejectUnauthorized: false }
});

async function debugMerchant() {
  const client = await pool.connect();
  try {
    console.log('🔍 Debugging T Z W CO.,LTD merchant...\n');
    
    // 1. Check merchant table
    console.log('1. Checking merchant table:');
    const merchantResult = await client.query(`
      SELECT merchant_id, merchant_vat, merchant_name 
      FROM merchant 
      WHERE merchant_name LIKE '%T Z W%' OR merchant_vat = '*************'
    `);
    console.log('Merchant data:', merchantResult.rows);
    
    if (merchantResult.rows.length > 0) {
      const merchantId = merchantResult.rows[0].merchant_id;
      
      // 2. Check merchant_bank table
      console.log('\n2. Checking merchant_bank table:');
      const merchantBankResult = await client.query(`
        SELECT mb.*, b.bank_code, b.bank_name_en, b.bank_name_th
        FROM merchant_bank mb
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id
        WHERE mb.merchant_id = $1
      `, [merchantId]);
      console.log('Merchant bank data:', merchantBankResult.rows);
      
      // 3. Check transaction_summary_report_detail
      console.log('\n3. Checking transaction_summary_report_detail:');
      const summaryResult = await client.query(`
        SELECT * FROM transaction_summary_report_detail 
        WHERE merchant_vat = '*************' OR merchant_name LIKE '%T Z W%'
        LIMIT 1
      `);
      console.log('Summary data:', summaryResult.rows);
      
      // 4. Test the exact query used in the application
      console.log('\n4. Testing application query:');
      const appQuery = await client.query(`
        SELECT
          d.merchant_vat, d.merchant_name, d.total_amount,
          mb.bank_id, b.bank_code, b.bank_name_th, b.bank_name_en,
          mb.bank_account_no, mb.bank_account_name
        FROM transaction_summary_report_detail d
        LEFT JOIN merchant m ON d.merchant_vat = m.merchant_vat
        LEFT JOIN merchant_bank mb ON m.merchant_id = mb.merchant_id AND mb.active = true
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id AND b.active = true
        WHERE d.merchant_vat = '*************' OR d.merchant_name LIKE '%T Z W%'
        LIMIT 1
      `);
      console.log('Application query result:', appQuery.rows);
      
      // 5. Check if there are multiple bank accounts
      console.log('\n5. Checking for multiple bank accounts:');
      const multipleAccounts = await client.query(`
        SELECT mb.*, b.bank_code, b.bank_name_en
        FROM merchant_bank mb
        LEFT JOIN tmst_bank b ON mb.bank_id = b.bank_id
        WHERE mb.merchant_id = $1
        ORDER BY mb.active DESC, mb.bank_id
      `, [merchantId]);
      console.log('All bank accounts for this merchant:', multipleAccounts.rows);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    client.release();
    pool.end();
  }
}

debugMerchant();
